---
allowed-tools: <PERSON><PERSON>(git add:*), <PERSON><PERSON>(git status:*), <PERSON><PERSON>(git commit:*), <PERSON><PERSON>(git push:*), <PERSON><PERSON>(git log:*), <PERSON><PERSON>(git branch:*), <PERSON><PERSON>(git diff:*), <PERSON><PERSON>(git remote:*), <PERSON><PERSON>(git config:*), <PERSON><PERSON>(git rev-parse:*)
description: Create a git commit with Chinese message and push to remote
---

## Context
- Current git status: !`git status --porcelain`
- Unstaged changes: !`git diff --name-only`
- Staged changes: !`git diff --cached --name-only`
- Full diff of staged changes: !`git diff --cached`
- Full diff of unstaged changes: !`git diff`
- Current branch: !`git branch --show-current`
- Remote branch info: !`git remote -v`
- Recent commits: !`git log --oneline -5`
- Repository root: !`git rev-parse --show-toplevel`
- Git user config: !`git config user.name && git config user.email`

## Your task
Based on the above information:

1. **Analyze the changes**: Review both staged and unstaged changes to understand what modifications were made
2. **Stage files if needed**: If there are unstaged changes that should be committed, add them using `git add`
3. **Add untracked files if needed**: If there are untracked files that should be committed, **ASK** the user if they want to add them using `git add`
3. **Create commit message**: Generate a concise but descriptive commit message in Chinese that accurately describes the changes
4. **Commit**: Use `git commit -m "your_message"` to create the commit
5. **Push**: Push the commit to the remote repository using `git push`

**Requirements for commit message**:
- Must be in Chinese
- Follow conventional commits format: `type(scope): 主要变更描述`
- Include detailed bullet points describing specific changes
- Each bullet point should start with `-` and describe one specific change

**Commit message format**:
```
type(scope): 主要变更描述
- 具体变更1的详细描述
- 具体变更2的详细描述
- 具体变更3的详细描述
```

**Common types**:
- `feat`: 新功能
- `fix`: 修复问题
- `refactor`: 重构代码
- `docs`: 文档更新
- `style`: 代码格式调整
- `test`: 测试相关
- `chore`: 构建/工具相关

**Example**:
```
feat(config): 更新销售订单分析配置和消息格式化优化
- 将销售订单分析AI模型从deepseek-v3-1切换为kimi-k2-turbo
- 修复useMessageFormatter中timestamp数字转换验证逻辑
- 优化消息ID格式化注释说明
```

Execute all steps automatically without asking for confirmation unless there are conflicts or errors.
