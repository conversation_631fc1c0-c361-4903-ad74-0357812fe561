#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并请求代码审查工具
从阿里云CodeUp URL解析合并请求信息，获取diff并进行AI代码审查
"""

import os
import re
import argparse
import fnmatch
from typing import Optional, Dict, Any

# 默认排除模式，可以通过环境变量 MR_REVIEW_EXCLUDE_PATTERNS 覆盖
DEFAULT_EXCLUDE_PATTERNS = [
    'test/java/',
    '*Test.java',
    '*.md',
    '*.txt',
    '*.json'
]

# OpenAI SDK导入
from openai import OpenAI

# 阿里云DevOps SDK导入
from alibabacloud_devops20210625.client import Client as devops20210625Client
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_devops20210625 import models as devops_20210625_models
from alibabacloud_tea_util import models as util_models

# LLM配置
LLM_API_KEY = os.getenv("XM_LLM_API_KEY", "")
LLM_BASE_URL = os.getenv("XM_LLM_BASE_URL", "https://litellm-test.summerfarm.net/v1")
LLM_MODEL = os.getenv("XM_LLM_MODEL", "deepseek-v3-250324")
NEED_COMMENT = os.getenv("XM_NEED_COMMENT", "false").lower() == "true"
NEED_FINAL_COMMENT = os.getenv("XM_NEED_FINAL_COMMENT", "true").lower() == "true"

def load_system_instruction(file_path: str = "code_review_instruction.txt") -> str:
    """从文件加载系统指令，如果文件不存在则使用默认指令"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read().strip()
    except FileNotFoundError:
        print(f"警告: 未找到系统指令文件 {file_path}，使用默认指令")
        # 默认指令作为备用
        return """你是一位经验丰富的资深代码评审专家。你将收到两部分信息：Git diff 输出和完整文件内容。请严格分析这些信息，只识别并报告最关键的问题。你的评审应重点关注：

1.  **严重 Bug：** 直接通过更改可识别的逻辑错误、崩溃或数据损坏风险。
2.  **重大安全漏洞：** 在代码中可见的常见弱点（例如，注入缺陷、访问控制失效、敏感数据暴露）。
3.  **主要性能瓶颈：** 引入显著低效的代码更改（例如，N+1 查询、处理大型数据集的低效循环），影响资源使用或延迟。
4.  **关键架构/设计缺陷：** 根据提供的上下文，引入显著耦合、违反核心设计原则或严重阻碍未来维护/扩展性的更改。

**重要指示：**
*   【一定要遵守】请使用中文回答。
*   从代码中推断编程语言，重点分析 diff 中的变更。
*   **严格忽略：** 次要代码风格问题、小错字、注释、文档、次要优化以及非关键建议。
*   按严重程度优先报告问题。
*   直接、专业、简洁。
*   如果根据这些标准**未**找到任何关键问题，则**只**回复："恭喜🎉！未发现任何严重问题。"
"""
    except Exception as e:
        print(f"读取系统指令文件时发生错误: {e}，使用默认指令")
        return "你是一位经验丰富的代码评审专家，请对提供的代码diff进行审查，重点关注严重bug、安全漏洞、性能问题和架构缺陷。请使用中文回答。"


class DevOpsClient:
    """阿里云DevOps客户端封装"""
    
    def __init__(self):
        self.client = self._create_client()
    
    @staticmethod
    def _create_client() -> devops20210625Client:
        """创建DevOps客户端"""
        credential = CredentialClient()
        config = open_api_models.Config(credential=credential)
        config.endpoint = 'devops.cn-hangzhou.aliyuncs.com'
        return devops20210625Client(config)
    
    def find_repository_by_path(self, organization_id: str, path_with_namespace: str) -> Optional[str]:
        """根据路径查找repository ID"""
        try:
            request = devops_20210625_models.ListRepositoriesRequest(
                organization_id=organization_id,
                search=path_with_namespace,
                page=1,
                per_page=20,
            )
            runtime = util_models.RuntimeOptions()
            headers = {}
            
            response = self.client.list_repositories_with_options(request, headers, runtime)
            
            if response.body and response.body.result:
                for repo in response.body.result:
                    if hasattr(repo, 'path_with_namespace') and repo.path_with_namespace == path_with_namespace:
                        return str(repo.id)
            
            print(f"未找到匹配的repository: {path_with_namespace}, response.body: {response.body}")
            return None
            
        except Exception as e:
            print(f"查找repository时发生错误: {e}")
            return None
    
    def get_merge_request_commits(self, organization_id: str, repository_id: str, local_id: int) -> Dict[str, Any]:
        """
        获取合并请求的commit ID和patch_set_biz_id，返回MERGE_SOURCE和MERGE_TARGET
        MERGE_SOURCE: 新代码（要被合并的分支，选择最大版本号）
        MERGE_TARGET: 旧代码（目标分支，通常是主分支，选择最小版本号）
        """
        try:
            request = devops_20210625_models.ListMergeRequestPatchSetsRequest(
                organization_id=organization_id,
                repository_identity=repository_id,
                local_id=local_id
            )
            runtime = util_models.RuntimeOptions()
            headers = {}

            response = self.client.list_merge_request_patch_sets_with_options(request, headers, runtime)
            print(f"获取合并请求的所有commit ID response: {response}")

            merge_source_patches = []
            merge_target_patches = []

            if response.body and response.body.result:
                for patch_set in response.body.result:
                    if hasattr(patch_set, 'commit_id') and hasattr(patch_set, 'related_merge_item_type'):
                        patch_info = {
                            'commit_id': patch_set.commit_id,
                            'patch_set_no': getattr(patch_set, 'patch_set_no', 1),
                            'patch_set_name': getattr(patch_set, 'patch_set_name', ''),
                            'created_at': getattr(patch_set, 'created_at', ''),
                            'patch_set_biz_id': getattr(patch_set, 'patch_set_biz_id', '')
                        }

                        if patch_set.related_merge_item_type == 'MERGE_SOURCE':
                            merge_source_patches.append(patch_info)
                        elif patch_set.related_merge_item_type == 'MERGE_TARGET':
                            merge_target_patches.append(patch_info)

            # 选择最大版本号的MERGE_SOURCE（最新的提交）
            merge_source_info = None
            if merge_source_patches:
                latest_source = max(merge_source_patches, key=lambda x: x['patch_set_no'])
                merge_source_info = latest_source
                print(f"选择MERGE_SOURCE: 版本{latest_source['patch_set_no']} ({latest_source['patch_set_name']}) - {latest_source['commit_id']} - {latest_source['patch_set_biz_id']}")

            # 选择最小版本号的MERGE_TARGET（基础版本）
            merge_target_info = None
            if merge_target_patches:
                earliest_target = min(merge_target_patches, key=lambda x: x['patch_set_no'])
                merge_target_info = earliest_target
                print(f"选择MERGE_TARGET: 版本{earliest_target['patch_set_no']} ({earliest_target['patch_set_name']}) - {earliest_target['commit_id']} - {earliest_target['patch_set_biz_id']}")

            # 显示所有版本信息
            if merge_source_patches:
                print(f"发现 {len(merge_source_patches)} 个MERGE_SOURCE版本:")
                for patch in sorted(merge_source_patches, key=lambda x: x['patch_set_no']):
                    print(f"  版本{patch['patch_set_no']}: {patch['patch_set_name']} - {patch['commit_id']} - {patch['patch_set_biz_id']}")

            if merge_target_patches:
                print(f"发现 {len(merge_target_patches)} 个MERGE_TARGET版本:")
                for patch in sorted(merge_target_patches, key=lambda x: x['patch_set_no']):
                    print(f"  版本{patch['patch_set_no']}: {patch['patch_set_name']} - {patch['commit_id']} - {patch['patch_set_biz_id']}")

            return {
                'merge_source': merge_source_info['commit_id'] if merge_source_info else None,
                'merge_target': merge_target_info['commit_id'] if merge_target_info else None,
                'merge_source_info': merge_source_info,
                'merge_target_info': merge_target_info
            }

        except Exception as e:
            print(f"获取合并请求commits时发生错误: {e}")
            return {'merge_source': None, 'merge_target': None, 'merge_source_info': None, 'merge_target_info': None}
    
    def get_compare_detail(self, repository_id: str, organization_id: str, from_commit: str, to_commit: str) -> Optional[Dict[str, Any]]:
        """获取两个commit之间的diff详情"""
        try:
            request = devops_20210625_models.GetCompareDetailRequest(
                from_=from_commit,
                to=to_commit,
                organization_id=organization_id
            )
            runtime = util_models.RuntimeOptions()
            headers = {}

            response = self.client.get_compare_detail_with_options(repository_id, request, headers, runtime)

            if response.body and response.body.result:
                return {
                    'diffs': response.body.result.diffs if hasattr(response.body.result, 'diffs') else [],
                    'commits': response.body.result.commits if hasattr(response.body.result, 'commits') else []
                }

            return None

        except Exception as e:
            print(f"获取diff详情时发生错误: {e}")
            return None

    def get_file_content(self, repository_id: str, organization_id: str, file_path: str, ref: str = 'master') -> Optional[str]:
        """获取指定文件的内容"""
        try:
            request = devops_20210625_models.GetFileBlobsRequest(
                organization_id=organization_id,
                file_path=file_path,
                ref=ref
            )
            runtime = util_models.RuntimeOptions()
            headers = {}

            response = self.client.get_file_blobs_with_options(repository_id, request, headers, runtime)

            if response.body and response.body.result:
                # 获取文件内容，通常在content字段中
                if hasattr(response.body.result, 'content'):
                    return response.body.result.content
                elif hasattr(response.body.result, 'blob_content'):
                    return response.body.result.blob_content

            return None

        except Exception as e:
            print(f"获取文件内容时发生错误 ({file_path}): {e}")
            return None

    def post_change_request_comment(self, organization_id: str, repository_id: str, local_id: int, content: str):
        """使用SDK向指定的合并请求发表评论 (基于CreateCommentRequest)"""
        try:
            # 构造请求体
            request = devops_20210625_models.CreateCommentRequest(
                organization_id=organization_id,
                repository_identity=repository_id,
                local_id=local_id,
                content=content,
                comment_type='GLOBAL_COMMENT'
            )
            
            runtime = util_models.RuntimeOptions()
            headers = {}  # SDK会自动处理认证，无需token
            
            print(f"\n正在使用SDK向合并请求 {local_id} 发表评论...")
            response = self.client.create_comment_with_options(request, headers, runtime)
            
            print("评论已成功发送到云效。")
            return response.body

        except Exception as e:
            print(f"使用SDK发送评论到云效时发生错误: {e}")
            return None

    def post_file_comment(self, organization_id: str, repository_id: str, local_id: int, file_path: str, content: str,
                         line_number: int = 1, patch_set_biz_id: str = None, to_patch_set_biz_id: str = None,
                         from_patch_set_biz_id: str = None, resolved: bool = None):
        """使用SDK向指定的合并请求的特定文件发表行内评论"""
        try:
            # 如果没有明确指定resolved状态，根据评论内容自动判断
            if resolved is None:
                # 如果评论内容表明没有严重问题，则自动标记为已解决
                resolved = "未发现任何严重问题" in content or "恭喜🎉" in content

            # 构建请求参数
            request_params = {
                'organization_id': organization_id,
                'repository_identity': repository_id,
                'local_id': local_id,
                'content': content,
                'file_path': file_path,
                'line_number': line_number,
                'comment_type': 'INLINE_COMMENT',
                'resolved': resolved
            }

            # 添加patch_set相关字段（如果提供）
            if patch_set_biz_id:
                request_params['patch_set_biz_id'] = patch_set_biz_id
            if to_patch_set_biz_id:
                request_params['to_patch_set_biz_id'] = to_patch_set_biz_id
            if from_patch_set_biz_id:
                request_params['from_pach_set_biz_id'] = from_patch_set_biz_id

            request = devops_20210625_models.CreateCommentRequest(**request_params)

            runtime = util_models.RuntimeOptions()
            headers = {}

            print(f"\n正在使用SDK向文件 {file_path} 的第 {line_number} 行发表评论...")
            print(f"  patch_set_biz_id: {patch_set_biz_id}")
            print(f"  to_patch_set_biz_id: {to_patch_set_biz_id}")
            print(f"  from_patch_set_biz_id: {from_patch_set_biz_id}")
            print(f"  resolved: {resolved}")

            response = self.client.create_comment_with_options(request, headers, runtime)

            if resolved:
                print(f"文件评论已成功发送到云效，并自动标记为已解决。")
            else:
                print(f"文件评论已成功发送到云效。")
            return response.body

        except Exception as e:
            print(f"使用SDK发送文件评论到云效时发生错误: {e}")
            return None


def parse_diff_line_numbers(diff_content: str) -> int:
    """
    解析diff内容，提取第一个新增或修改行的行号
    返回适合用于评论的行号
    """
    if not diff_content:
        return 1

    lines = diff_content.split('\n')
    current_new_line = 1
    found_hunk = False

    for line in lines:
        # 解析diff头部信息，例如: @@ -10,7 +10,8 @@
        if line.startswith('@@'):
            # 提取新文件的起始行号
            match = re.search(r'\+(\d+)', line)
            if match:
                current_new_line = int(match.group(1))
                found_hunk = True
                continue

        # 只有在找到hunk头部后才开始计算行号
        if not found_hunk:
            continue

        # 如果是新增行（以+开头，但不是+++）
        if line.startswith('+') and not line.startswith('+++'):
            return current_new_line

        # 如果是上下文行（不以+或-开头）或删除行，行号递增
        if not line.startswith('-') or line.startswith('---'):
            current_new_line += 1
        # 删除行不影响新文件的行号

    # 如果没有找到新增行，返回diff中第一个有效行号
    return max(1, current_new_line - 1 if found_hunk else 1)


class URLParser:
    """URL解析器"""

    @staticmethod
    def parse_codeup_url(url: str) -> Optional[Dict[str, str]]:
        """
        解析CodeUp URL，支持两种格式：
        1. https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255
        2. https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/python/chatbi/change/7/diffs
        """
        # 修改正则表达式，支持多层项目路径和可选的 /diffs 后缀
        # 使用非贪婪匹配来正确分离项目路径和 /change/ 部分
        pattern = r'https://codeup\.aliyun\.com/([^/]+)/(.+?)/change/(\d+)(?:/diffs)?$'
        match = re.match(pattern, url)

        if match:
            organization_id = match.group(1)
            project_path = match.group(2)  # 这可能包含多层路径，如 "tech/python/chatbi"
            local_id = match.group(3)

            # 将项目路径分解为 namespace 和 project_name
            # 假设第一层是 namespace，其余是 project_name
            path_parts = project_path.split('/')
            if len(path_parts) >= 2:
                namespace = path_parts[0]
                project_name = '/'.join(path_parts[1:])
            else:
                # 如果只有一层，则作为 namespace，project_name 为空
                namespace = path_parts[0]
                project_name = ''

            path_with_namespace = f"{organization_id}/{namespace}/{project_name}" if project_name else f"{organization_id}/{namespace}"

            return {
                'organization_id': organization_id,
                'path_with_namespace': path_with_namespace,
                'local_id': int(local_id)
            }

        return None


class AICodeReviewer:
    """AI代码审查器"""

    def __init__(self, model: Optional[str] = None):
        self.api_key = LLM_API_KEY
        self.base_url = LLM_BASE_URL
        self.model = model or LLM_MODEL
        self.system_instruction = load_system_instruction()

        # 初始化OpenAI客户端
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
    
    def review_diff(self, diff_content: str, file_path: str, full_file_content: Optional[str] = None) -> str:
        """对diff内容进行AI审查，可选择性地包含完整文件内容"""
        if len(diff_content.strip()) <= 10:
            return f"diff内容过少，跳过审查: {file_path}"

        # 构建用户消息内容
        print(f"\n\n开始AI评审文件:{file_path}\n{diff_content}\n")

        try:
            # 使用OpenAI SDK创建流式响应
            stream = self.client.chat.completions.create(
                model=self.model,
                max_tokens=16384,
                top_p=0.1,
                presence_penalty=0,
                frequency_penalty=0,
                temperature=0.05,
                stream=True,
                seed=42,  # 固定seed确保可重现性
                messages=[
                    {
                        "role": "system",
                        "content": self.system_instruction,
                    },
                    {
                        "role": "user",
                        "content": f"=== 以下是 {file_path}的完整文件内容（用于更好理解上下文） ===\n\n{full_file_content}",
                    },
                    {
                        "role": "assistant",
                        "content": "好的，请给我diff内容",
                    },
                    {
                        "role": "user",
                        "content": f"以下是文件 {file_path} 的diff内容，请进行代码审查：\n\n{diff_content}",
                    },
                ],
            )

            full_content = []
            reasoning_content_ended = False
            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    content_piece = chunk.choices[0].delta.content
                    full_content.append(content_piece)
                    if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content is not None:
                        print(chunk.choices[0].delta.reasoning_content, end='', flush=True)
                        reasoning_content_ended = False
                    else:
                        # reasoning_content 首次结束才需要打印分割线
                        if reasoning_content_ended is False:
                            print("\n\n=== AI评审结果===\n")
                        reasoning_content_ended = True
                    if reasoning_content_ended:
                        print(content_piece, end='', flush=True)
            return "".join(full_content) if full_content else "AI审查返回空内容"

        except Exception as e:
            return f"AI审查失败: {str(e)}"

    def generate_final_summary(self, markdown_content: str) -> str:
        """对完整的代码审查markdown内容进行最终总结"""

        # 总结专用的系统提示词
        summary_instruction = """你是一位资深的代码审查专家。现在需要你对一份完整的代码审查报告进行最终总结。

请仔细分析提供的markdown格式的代码审查报告，并按照以下格式输出总结：

## 📋 审查文件列表
列出本次审查涉及的所有文件

## 🚨 必须修改的问题
列出所有严重的、必须要修改的问题，包括：
- 文件名
- 具体问题描述
- 修改建议

## ⚠️ 次要问题
列出可改可不改的次要问题，包括：
- 文件名
- 问题描述
- 改进建议

## 📊 总体评估
对本次代码审查的整体评估和建议

请使用中文回答，保持专业和简洁。如果某个分类下没有问题，请明确说明"无"。"""

        user_content = f"以下是完整的代码审查报告，请进行最终总结：\n\n{markdown_content}"

        try:
            print("\n\n=== 正在生成最终总结 ===")

            # 使用OpenAI SDK创建流式响应
            stream = self.client.chat.completions.create(
                model=self.model,
                max_tokens=8192,
                top_p=1,
                presence_penalty=0,
                frequency_penalty=0,
                temperature=0.7,
                stream=True,
                messages=[
                    {
                        "role": "system",
                        "content": summary_instruction,
                    },
                    {
                        "role": "user",
                        "content": user_content,
                    },
                ],
            )

            full_content = []
            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    content_piece = chunk.choices[0].delta.content
                    print(content_piece, end='', flush=True)
                    full_content.append(content_piece)

            return "".join(full_content) if full_content else "AI总结返回空内容"

        except Exception as e:
            return f"AI总结失败: {str(e)}"


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="合并请求代码审查工具")
    parser.add_argument(
        "--model",
        type=str,
        help=f"指定AI模型 (默认: {LLM_MODEL})"
    )
    parser.add_argument(
        "--url",
        type=str,
        help="CodeUp合并请求URL，支持多个URL用逗号分隔"
    )
    return parser.parse_args()


def process_single_url(url: str, model: Optional[str], exclude_patterns: list):
    """处理单个合并请求URL的完整审查流程"""
    print(f"\n{'='*20}\n开始处理URL: {url}\n{'='*20}")

    # 解析URL
    parser = URLParser()
    url_info = parser.parse_codeup_url(url)
    
    if not url_info:
        print(f"无法解析URL: {url}，请确保URL格式正确")
        print("支持的URL格式:")
        print("  https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255")
        print("  https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs")
        return
    
    print(f"解析结果:")
    print(f"  组织ID: {url_info['organization_id']}")
    print(f"  项目路径: {url_info['path_with_namespace']}")
    print(f"  合并请求ID: {url_info['local_id']}")
    
    # 初始化DevOps客户端
    devops_client = DevOpsClient()
    
    # 1. 获取repository ID
    print("\n1. 查找repository...")
    repo_id = devops_client.find_repository_by_path(
        url_info['organization_id'], 
        url_info['path_with_namespace']
    )
    
    if not repo_id:
        print("无法找到对应的repository")
        return
    
    print(f"找到repository ID: {repo_id}")
    
    # 2. 获取合并请求的commit IDs和patch_set_biz_id
    print("\n2. 获取合并请求的commits...")
    commits_info = devops_client.get_merge_request_commits(
        url_info['organization_id'],
        repo_id,
        url_info['local_id']
    )

    merge_source = commits_info.get('merge_source')
    merge_target = commits_info.get('merge_target')
    merge_source_info = commits_info.get('merge_source_info')
    merge_target_info = commits_info.get('merge_target_info')

    if not merge_source or not merge_target:
        print("未找到MERGE_SOURCE或MERGE_TARGET commit")
        print(f"MERGE_SOURCE: {merge_source}")
        print(f"MERGE_TARGET: {merge_target}")
        return

    print(f"找到commits:")
    print(f"  MERGE_SOURCE (新代码): {merge_source}")
    print(f"  MERGE_TARGET (旧代码): {merge_target}")

    # 提取patch_set_biz_id信息
    to_patch_set_biz_id = merge_source_info.get('patch_set_biz_id') if merge_source_info else None
    from_patch_set_biz_id = merge_target_info.get('patch_set_biz_id') if merge_target_info else None
    patch_set_biz_id = to_patch_set_biz_id  # 通常使用to_patch_set_biz_id作为主要的patch_set_biz_id

    print(f"  patch_set_biz_id: {patch_set_biz_id}")
    print(f"  to_patch_set_biz_id: {to_patch_set_biz_id}")
    print(f"  from_patch_set_biz_id: {from_patch_set_biz_id}")

    # 3. 获取diff并进行AI审查
    print("\n3. 开始代码审查...")

    from_commit = merge_target
    to_commit = merge_source

    print(f"比较范围: {from_commit} (旧) -> {to_commit} (新)")

    compare_result = devops_client.get_compare_detail(
        repo_id,
        url_info['organization_id'],
        from_commit,
        to_commit
    )

    if compare_result and compare_result.get('diffs'):
        ai_reviewer = AICodeReviewer(model=model)
        model_info = model if model else LLM_MODEL
        print(f"使用AI模型: {model_info}")

        overall_markdown = f"# 代码审查报告\n\n**合并请求**: {url}\n**使用模型**: {model_info}\n\n"

        for diff in compare_result['diffs']:
            if hasattr(diff, 'new_path') and hasattr(diff, 'diff'):
                file_path = diff.new_path
                diff_content = diff.diff

                should_skip = any(
                    fnmatch.fnmatch(file_path, pattern) or (pattern.endswith('/') and file_path.startswith(pattern))
                    for pattern in exclude_patterns
                )

                if should_skip:
                    print(f"\n跳过文件 (匹配排除模式): {file_path}")
                    continue

                print(f"\n审查文件: {file_path}")
                overall_markdown += f"## 文件: {file_path}\n\n"

                print(f"  获取文件完整内容...")
                full_file_content = devops_client.get_file_content(
                    repo_id,
                    url_info['organization_id'],
                    file_path,
                    from_commit
                )

                if full_file_content:
                    print(f"  已获取完整文件内容 ({len(full_file_content)} 字符)")
                else:
                    print(f"  未能获取完整文件内容，仅使用diff进行审查")

                review_result = ai_reviewer.review_diff(diff_content, file_path, full_file_content)
                overall_markdown += f"{review_result}\n\n"

                # 检查是否需要发表评论
                has_serious_issues = "未发现任何严重问题" not in review_result and "恭喜🎉" not in review_result

                if NEED_COMMENT and has_serious_issues:
                    print(f"\n在文件 {file_path} 中发现问题，正在发表文件评论...")
                    
                    # 解析diff内容获取合适的行号
                    comment_line_number = parse_diff_line_numbers(diff_content)
                    print(f"  使用行号: {comment_line_number}")

                    devops_client.post_file_comment(
                        organization_id=url_info['organization_id'],
                        repository_id=repo_id,
                        local_id=url_info['local_id'],
                        file_path=file_path,
                        content=review_result,
                        line_number=comment_line_number,
                        patch_set_biz_id=patch_set_biz_id,
                        to_patch_set_biz_id=to_patch_set_biz_id,
                        from_patch_set_biz_id=from_patch_set_biz_id
                        # resolved参数会在post_file_comment方法内部自动根据content判断
                    )
                elif NEED_COMMENT:
                    print(f"\n在文件 {file_path} 中未发现严重问题，跳过文件评论")
                else:
                    print(f"\n未设置NEED_COMMENT，跳过评论")

        print(f"\n4. 生成最终总结...")
        final_summary = ai_reviewer.generate_final_summary(overall_markdown)

        final_comment_content = f"# AI代码审查报告\n\n**合并请求**: {url}\n**使用模型**: {model_info}\n**生成时间**: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n---\n\n{final_summary}"

        if NEED_FINAL_COMMENT:
            print(f"\n5. 发送评论到云效...")
            devops_client.post_change_request_comment(
                url_info['organization_id'],
                repo_id,
                url_info['local_id'],
                final_comment_content
            )
            print(f"\n审查完成！评论已发送至 {url}")
        else:
            print(f"\n未设置NEED_FINAL_COMMENT，跳过评论")

        print(f"\n审查完成！请查看合并请求 {url} 以查看详细报告和最终总结。")
    else:
        print("未获取到diff信息")


def main():
    """主函数"""
    print("=== 合并请求代码审查工具 ===")

    # 解析命令行参数
    args = parse_arguments()

    # 获取排除模式
    exclude_patterns_str = os.getenv("MR_REVIEW_EXCLUDE_PATTERNS")
    if exclude_patterns_str:
        exclude_patterns = [p.strip() for p in exclude_patterns_str.split(',') if p.strip()]
        print(f"使用环境变量 MR_REVIEW_EXCLUDE_PATTERNS 定义的排除模式: {exclude_patterns}")
    else:
        exclude_patterns = DEFAULT_EXCLUDE_PATTERNS
        print(f"使用默认排除模式: {exclude_patterns}")

    # 从命令行 --url 参数获取URL列表
    if args.url:
        # 支持逗号分隔的多个URL
        urls = [url.strip() for url in args.url.split(',') if url.strip()]
        if len(urls) == 1:
            print(f"使用命令行提供的URL: {urls[0]}")
        else:
            print(f"使用命令行提供的 {len(urls)} 个URL")
    else:
        # 如果没有提供--url参数，则交互式输入
        url_input = input("请输入一个或多个CodeUp合并请求URL (用逗号分隔): ").strip()
        if not url_input:
            print("URL不能为空")
            return
        urls = [url.strip() for url in url_input.split(',') if url.strip()]

    if not urls:
        print("未提供任何有效的URL")
        return

    # 循环处理每个URL
    for url in urls:
        try:
            process_single_url(url, args.model, exclude_patterns)
        except Exception as e:
            print(f"\n处理URL {url} 时发生严重错误: {e}")
            # 选择继续处理下一个URL
            continue

    print("\n所有URL处理完毕。")


if __name__ == "__main__":
    main()
